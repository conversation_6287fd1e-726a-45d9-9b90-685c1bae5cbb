import 'package:flutter/material.dart';

// Modern refined color palette
const Color primaryBlue = Color(0xFF0A84FF);
const Color primaryGreen = Color(0xFF8cc051);
const Color primaryTeal = Color(0xFF40BFBF);
const Color deepNavy = Color(0xFF0A1A2F);
const Color vibrantPurple = Color(0xFF5E5CE6);
const Color electricYellow = Color(0xFFFFD60A);
const Color softGreen = Color(0xFF4CD964);
const Color inactiveGrey = Color(0xFF8E8E93);
const Color backgroundStart = Color(0xFF042448);
const Color backgroundEnd = Color(0xFF050A18);
const Color cardLight = Color(0xFF112D4E);
const Color cardDark = Color(0xFF0A1A2F);
const Color glowColor = Color(0x3340BFBF);

/// Utility functions for working with colors
class ColorUtils {
  /// Creates a new color with the given opacity value
  /// This is a replacement for the deprecated withOpacity method
  static Color withOpacityValue(Color color, double opacity) {
    // Use withAlpha which is not deprecated
    return color.withAlpha((opacity * 255).round());
  }
}

/// Extension method for Color to make it easier to use the withOpacityValue function
extension ColorExtension on Color {
  /// Creates a new color with the given opacity value
  /// This is a replacement for the deprecated withOpacity method
  Color withOpacityValue(double opacity) {
    // Use the static method to avoid duplication
    return ColorUtils.withOpacityValue(this, opacity);
  }
}
