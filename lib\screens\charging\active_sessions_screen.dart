import 'package:flutter/material.dart';
import 'package:liquid_glass_renderer/liquid_glass_renderer.dart';
import '../../models/ongoing_session.dart';
import '../../services/ongoing_sessions_service.dart';
import '../../utils/app_themes.dart';

/// Screen displaying list of active charging sessions
class ActiveSessionsScreen extends StatefulWidget {
  const ActiveSessionsScreen({super.key});

  @override
  State<ActiveSessionsScreen> createState() => _ActiveSessionsScreenState();
}

class _ActiveSessionsScreenState extends State<ActiveSessionsScreen> {
  final OngoingSessionsService _ongoingSessionsService =
      OngoingSessionsService();
  List<OngoingSession> _activeSessions = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadActiveSessions();
  }

  Future<void> _loadActiveSessions() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await _ongoingSessionsService.getOngoingSessions();

      if (response != null && response.success) {
        setState(() {
          _activeSessions = response.data;
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = 'Failed to load active sessions';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading sessions: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _refreshSessions() async {
    await _loadActiveSessions();
  }

  void _onSessionTapped(OngoingSession session) {
    debugPrint('🔋 ===== SESSION CARD TAPPED =====');
    debugPrint('🔋 Session ID (Transaction ID): ${session.id}');
    debugPrint('🔋 Authorization Reference: ${session.authorizationReference}');
    debugPrint('🔋 Charger UID: ${session.chargerUid}');
    debugPrint('🔋 Connector ID: ${session.connectorId}');
    debugPrint('🔋 Station Name: ${session.charger.chargerName}');
    debugPrint('🔋 Status: ${session.status}');
    debugPrint('🔋 Invoice Number: ${session.invoiceNumber}');
    debugPrint('🔋 ===== NAVIGATION DATA =====');
    debugPrint('🔋 Transaction ID for Stop API: ${session.id}');
    debugPrint('🔋 Transaction ID Type: ${session.id.runtimeType}');
    debugPrint(
        '🔋 Authorization Reference for Data Polling: ${session.authorizationReference}');
    debugPrint('🔋 Expected Stop Endpoint: /user/sessions/stop/${session.id}');
    debugPrint(
        '🔋 Expected Data Polling Endpoint: /user/sessions/on-going-data?authorization_reference=${session.authorizationReference}');

    // Navigate to charging session screen with the authorization reference as the primary identifier
    Navigator.pushNamed(
      context,
      '/charging_session',
      arguments: {
        'station_uid': session.chargerUid,
        'connector_id': session.connectorId,
        'charge_percentage': 0.0, // Will be updated by real-time data polling
        'verified_session_data': {
          // The authorization_reference is the key identifier for data polling
          'authorization_reference': session.authorizationReference,
          // CRITICAL: Use the 'id' field as transaction_id for stop transaction API
          'id': session.id, // This is the actual transaction ID (e.g., 30964)
          'transaction_id':
              session.id.toString(), // String version for compatibility
          'charger_uid': session.chargerUid,
          'connector_id': session.connectorId,
          'invoice_number': session.invoiceNumber,
          'status': session.status,
          'charger_name': session.charger.chargerName,
          'charging_rate': session.evChargingRate,
          'booking_id': session.bookingId,
          'created_at': session.createdAt.toIso8601String(),
          // Mark this as an ongoing session (not a new session)
          'is_ongoing_session': true,
        },
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDarkMode ? const Color(0xFF121212) : Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Active Charging Sessions',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        backgroundColor: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        foregroundColor: isDarkMode ? Colors.white : Colors.black,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshSessions,
            tooltip: 'Refresh Sessions',
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: AppThemes.primaryColor),
            SizedBox(height: 16),
            Text(
              'Loading active sessions...',
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _refreshSessions,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_activeSessions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.battery_charging_full,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            const Text(
              'No Active Charging Sessions',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Start charging at any station to see your active sessions here.',
              style: TextStyle(
                fontSize: 16,
                color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    final screenWidth = MediaQuery.of(context).size.width;

    return RefreshIndicator(
      onRefresh: _refreshSessions,
      color: AppThemes.primaryColor,
      child: ListView.builder(
        padding: EdgeInsets.all(screenWidth < 360 ? 12 : 16),
        itemCount: _activeSessions.length,
        itemBuilder: (context, index) {
          final session = _activeSessions[index];
          return _buildSessionCard(session);
        },
      ),
    );
  }

  Widget _buildSessionCard(OngoingSession session) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final screenWidth = MediaQuery.of(context).size.width;
    final isCompact = screenWidth < 360; // Small screen detection

    // Responsive padding and spacing
    final cardPadding = isCompact ? 16.0 : 20.0;
    final iconSize = isCompact ? 20.0 : 24.0;
    final spacing = isCompact ? 8.0 : 12.0;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: LiquidGlass(
        settings: LiquidGlassSettings(
          thickness: 18,
          glassColor:
              isDarkMode ? const Color(0x18FFFFFF) : const Color(0x12000000),
          lightIntensity: 1.8,
          ambientStrength: 0.4,
          blend: 40,
          lightAngle: 1.0,
        ),
        shape: LiquidRoundedSuperellipse(
          borderRadius: Radius.circular(16),
        ),
        glassContainsChild: true,
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () => _onSessionTapped(session),
            borderRadius: BorderRadius.circular(16),
            child: Container(
              padding: EdgeInsets.all(cardPadding),
              decoration: BoxDecoration(
                color: isDarkMode
                    ? Colors.white.withValues(alpha: 0.05)
                    : Colors.white.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: isDarkMode
                      ? Colors.white.withValues(alpha: 0.1)
                      : Colors.black.withValues(alpha: 0.1),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildCardHeader(
                      session, isDarkMode, iconSize, spacing, isCompact),
                  SizedBox(height: isCompact ? 12 : 16),
                  _buildInfoGrid(session, isCompact),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Builds the responsive card header with station name, connector, and status
  Widget _buildCardHeader(OngoingSession session, bool isDarkMode,
      double iconSize, double spacing, bool isCompact) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isVeryCompact = screenWidth < 320; // Very small screens

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Station icon
        Container(
          padding: EdgeInsets.all(isCompact ? 6 : 8),
          decoration: BoxDecoration(
            color: AppThemes.primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.ev_station,
            color: AppThemes.primaryColor,
            size: iconSize,
          ),
        ),
        SizedBox(width: spacing),

        // Station name and connector info
        Expanded(
          flex: isVeryCompact ? 2 : 3,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Station name with responsive text sizing
              Text(
                session.charger.chargerName,
                style: TextStyle(
                  fontSize: isCompact ? 14 : 16,
                  fontWeight: FontWeight.w600,
                  color: isDarkMode
                      ? AppThemes.darkTextPrimary
                      : AppThemes.lightTextPrimary,
                  height: 1.2, // Better line height for readability
                ),
                maxLines: isCompact ? 1 : 2,
                overflow: TextOverflow.ellipsis,
                softWrap: true,
              ),
              SizedBox(height: isCompact ? 2 : 4),

              // Connector info
              Text(
                'Connector ${session.connectorId}',
                style: TextStyle(
                  fontSize: isCompact ? 12 : 14,
                  color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),

        // Status badge with responsive sizing
        Flexible(
          child: Container(
            constraints: BoxConstraints(
              maxWidth: isVeryCompact ? 60 : 80,
            ),
            padding: EdgeInsets.symmetric(
              horizontal: isCompact ? 6 : 8,
              vertical: isCompact ? 3 : 4,
            ),
            decoration: BoxDecoration(
              color: _getStatusColor(session.status).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              session.status.toUpperCase(),
              style: TextStyle(
                fontSize: isCompact ? 10 : 12,
                fontWeight: FontWeight.w600,
                color: _getStatusColor(session.status),
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
      ],
    );
  }

  /// Builds the responsive info grid with session details
  Widget _buildInfoGrid(OngoingSession session, bool isCompact) {
    final screenWidth = MediaQuery.of(context).size.width;
    final useVerticalLayout =
        screenWidth < 300; // Very small screens use vertical layout

    if (useVerticalLayout) {
      // Vertical layout for very small screens
      return Column(
        children: [
          _buildInfoItem('Charging Rate', '${session.evChargingRate} kW',
              Icons.flash_on, isCompact),
          SizedBox(height: isCompact ? 8 : 12),
          _buildInfoItem('Session ID', '#${session.id}',
              Icons.confirmation_number, isCompact),
          SizedBox(height: isCompact ? 8 : 12),
          _buildInfoItem('Started', _formatDateTime(session.createdAt),
              Icons.access_time, isCompact),
          SizedBox(height: isCompact ? 8 : 12),
          _buildInfoItem(
              'Invoice', session.invoiceNumber, Icons.receipt, isCompact),
        ],
      );
    }

    // Standard 2x2 grid layout
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildInfoItem('Charging Rate',
                  '${session.evChargingRate} kW', Icons.flash_on, isCompact),
            ),
            SizedBox(width: isCompact ? 8 : 16),
            Expanded(
              child: _buildInfoItem('Session ID', '#${session.id}',
                  Icons.confirmation_number, isCompact),
            ),
          ],
        ),
        SizedBox(height: isCompact ? 8 : 12),
        Row(
          children: [
            Expanded(
              child: _buildInfoItem(
                  'Started',
                  _formatDateTime(session.createdAt),
                  Icons.access_time,
                  isCompact),
            ),
            SizedBox(width: isCompact ? 8 : 16),
            Expanded(
              child: _buildInfoItem(
                  'Invoice', session.invoiceNumber, Icons.receipt, isCompact),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon,
      [bool isCompact = false]) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final screenWidth = MediaQuery.of(context).size.width;
    final isVeryCompact = screenWidth < 320;

    // Responsive sizing
    final iconSize = isCompact ? 14.0 : 16.0;
    final labelFontSize = isCompact ? 10.0 : 12.0;
    final valueFontSize = isCompact ? 11.0 : 13.0;
    final spacing = isCompact ? 4.0 : 6.0;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Icon with responsive sizing
        Icon(
          icon,
          size: iconSize,
          color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
        ),
        SizedBox(width: spacing),

        // Text content with flexible layout
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Label with responsive font size
              Text(
                label,
                style: TextStyle(
                  fontSize: labelFontSize,
                  color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                  height: 1.2,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),

              // Small spacing between label and value
              SizedBox(height: isCompact ? 1 : 2),

              // Value with smart overflow handling
              _buildValueText(value, valueFontSize, isDarkMode, isVeryCompact),
            ],
          ),
        ),
      ],
    );
  }

  /// Builds the value text with intelligent overflow handling
  Widget _buildValueText(
      String value, double fontSize, bool isDarkMode, bool isVeryCompact) {
    // Special handling for different value types
    final isLongSessionId = value.startsWith('#') && value.length > 8;
    final isLongInvoice = value.length > 15;

    // Determine max lines based on content type and screen size
    int maxLines = 1;
    if (isVeryCompact && (isLongSessionId || isLongInvoice)) {
      maxLines = 2; // Allow wrapping for long IDs on very small screens
    }

    return Text(
      value,
      style: TextStyle(
        fontSize: fontSize,
        fontWeight: FontWeight.w500,
        color:
            isDarkMode ? AppThemes.darkTextPrimary : AppThemes.lightTextPrimary,
        height: maxLines > 1 ? 1.2 : 1.3,
      ),
      maxLines: maxLines,
      overflow: TextOverflow.ellipsis,
      softWrap: maxLines > 1,
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
      case 'charging':
        return const Color(0xFF8cc051); // Use lime green for charging status
      case 'pending':
        return Colors.orange;
      case 'completed':
        return Colors.blue;
      case 'error':
      case 'failed':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }
}
